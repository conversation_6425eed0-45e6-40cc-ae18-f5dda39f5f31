<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<style type="text/css">
body {background-color: #fff; color: #222; font-family: sans-serif;}
pre {margin: 0; font-family: monospace;}
a:link {color: #009; text-decoration: none; background-color: #fff;}
a:hover {text-decoration: underline;}
table {border-collapse: collapse; border: 0; width: 934px; box-shadow: 1px 2px 3px #ccc;}
.center {text-align: center;}
.center table {margin: 1em auto; text-align: left;}
.center th {text-align: center !important;}
td, th {border: 1px solid #666; font-size: 75%; vertical-align: baseline; padding: 4px 5px;}
th {position: sticky; top: 0; background: inherit;}
h1 {font-size: 150%;}
h2 {font-size: 125%;}
.p {text-align: left;}
.e {background-color: #ccf; width: 300px; font-weight: bold;}
.h {background-color: #99c; font-weight: bold;}
.v {background-color: #ddd; max-width: 300px; overflow-x: auto; word-wrap: break-word;}
.v i {color: #999;}
img {float: right; border: 0;}
hr {width: 934px; background-color: #ccc; border: 0; height: 1px;}
</style>
<title>phpinfo()</title><meta name="ROBOTS" content="NOINDEX,NOFOLLOW,NOARCHIVE,NOSNIPPET" />
</head>
<body><div class="center">
<table>
<tr class="h"><td>
<a href="http://www.php.net/"><img border="0" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHkAAAA5CAYAAAAjHpHIAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDozMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDozMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//2Q==" alt="PHP Logo" /></a><h1 class="p">PHP Version 7.4.33</h1>
</td></tr>
</table>

<table>
<tr><td class="e">System </td><td class="v">Linux webserver 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 </td></tr>
<tr><td class="e">Build Date </td><td class="v">Jun  7 2023 15:54:13 </td></tr>
<tr><td class="e">Server API </td><td class="v">Apache 2.0 Handler </td></tr>
<tr><td class="e">Virtual Directory Support </td><td class="v">disabled </td></tr>
<tr><td class="e">Configuration File (php.ini) Path </td><td class="v">/etc/php/7.4/apache2 </td></tr>
<tr><td class="e">Loaded Configuration File </td><td class="v">/etc/php/7.4/apache2/php.ini </td></tr>
<tr><td class="e">Scan this dir for additional .ini files </td><td class="v">/etc/php/7.4/apache2/conf.d </td></tr>
<tr><td class="e">Additional .ini files parsed </td><td class="v">/etc/php/7.4/apache2/conf.d/10-mysqlnd.ini,<br />/etc/php/7.4/apache2/conf.d/10-opcache.ini,<br />/etc/php/7.4/apache2/conf.d/10-pdo.ini,<br />/etc/php/7.4/apache2/conf.d/20-calendar.ini,<br />/etc/php/7.4/apache2/conf.d/20-ctype.ini,<br />/etc/php/7.4/apache2/conf.d/20-curl.ini,<br />/etc/php/7.4/apache2/conf.d/20-dom.ini,<br />/etc/php/7.4/apache2/conf.d/20-exif.ini,<br />/etc/php/7.4/apache2/conf.d/20-ffi.ini,<br />/etc/php/7.4/apache2/conf.d/20-fileinfo.ini,<br />/etc/php/7.4/apache2/conf.d/20-ftp.ini,<br />/etc/php/7.4/apache2/conf.d/20-gd.ini,<br />/etc/php/7.4/apache2/conf.d/20-gettext.ini,<br />/etc/php/7.4/apache2/conf.d/20-iconv.ini,<br />/etc/php/7.4/apache2/conf.d/20-json.ini,<br />/etc/php/7.4/apache2/conf.d/20-mbstring.ini,<br />/etc/php/7.4/apache2/conf.d/20-mysqli.ini,<br />/etc/php/7.4/apache2/conf.d/20-pdo_mysql.ini,<br />/etc/php/7.4/apache2/conf.d/20-phar.ini,<br />/etc/php/7.4/apache2/conf.d/20-posix.ini,<br />/etc/php/7.4/apache2/conf.d/20-readline.ini,<br />/etc/php/7.4/apache2/conf.d/20-shmop.ini,<br />/etc/php/7.4/apache2/conf.d/20-simplexml.ini,<br />/etc/php/7.4/apache2/conf.d/20-sockets.ini,<br />/etc/php/7.4/apache2/conf.d/20-sysvmsg.ini,<br />/etc/php/7.4/apache2/conf.d/20-sysvsem.ini,<br />/etc/php/7.4/apache2/conf.d/20-sysvshm.ini,<br />/etc/php/7.4/apache2/conf.d/20-tokenizer.ini,<br />/etc/php/7.4/apache2/conf.d/20-xml.ini,<br />/etc/php/7.4/apache2/conf.d/20-xmlreader.ini,<br />/etc/php/7.4/apache2/conf.d/20-xmlwriter.ini,<br />/etc/php/7.4/apache2/conf.d/20-xsl.ini,<br />/etc/php/7.4/apache2/conf.d/20-zip.ini </td></tr>
<tr><td class="e">PHP API </td><td class="v">20190902 </td></tr>
<tr><td class="e">PHP Extension </td><td class="v">20190902 </td></tr>
<tr><td class="e">Zend Extension </td><td class="v">320190902 </td></tr>
<tr><td class="e">Zend Extension Build </td><td class="v">API320190902,NTS </td></tr>
<tr><td class="e">PHP Extension Build </td><td class="v">API20190902,NTS </td></tr>
<tr><td class="e">Debug Build </td><td class="v">no </td></tr>
<tr><td class="e">Thread Safety </td><td class="v">disabled </td></tr>
<tr><td class="e">Zend Signal Handling </td><td class="v">enabled </td></tr>
<tr><td class="e">Zend Memory Manager </td><td class="v">enabled </td></tr>
<tr><td class="e">Zend Multibyte Support </td><td class="v">provided by mbstring </td></tr>
<tr><td class="e">IPv6 Support </td><td class="v">enabled </td></tr>
<tr><td class="e">DTrace Support </td><td class="v">available, disabled </td></tr>
<tr><td class="e">Registered PHP Streams</td><td class="v">https, ftps, compress.zlib, compress.bz2, php, file, glob, data, http, ftp, phar, zip</td></tr>
<tr><td class="e">Registered Stream Socket Transports</td><td class="v">tcp, udp, unix, udg, ssl, tls, tlsv1.0, tlsv1.1, tlsv1.2, tlsv1.3</td></tr>
<tr><td class="e">Registered Stream Filters</td><td class="v">zlib.*, bzip2.*, convert.iconv.*, string.rot13, string.toupper, string.tolower, string.strip_tags, convert.*, consumed, dechunk, mcrypt.*, mdecrypt.*</td></tr>
</table>

<h2><a name="module_apache2handler">apache2handler</a></h2>
<table>
<tr><td class="e">Apache Version </td><td class="v">Apache/2.4.41 (Ubuntu) </td></tr>
<tr><td class="e">Apache API Version </td><td class="v">20120211 </td></tr>
<tr><td class="e">Server Administrator </td><td class="v">webmaster@localhost </td></tr>
<tr><td class="e">Hostname:Port </td><td class="v">localhost:8081 </td></tr>
<tr><td class="e">User/Group </td><td class="v">www-data(33)/33 </td></tr>
<tr><td class="e">Max Requests </td><td class="v">Per Child: 0 - Keep Alive: on - Max Per Connection: 100 </td></tr>
<tr><td class="e">Timeouts </td><td class="v">Connection: 300 - Keep-Alive: 5 </td></tr>
<tr><td class="e">Virtual Server </td><td class="v">Yes </td></tr>
<tr><td class="e">Server Root </td><td class="v">/etc/apache2 </td></tr>
<tr><td class="e">Loaded Modules </td><td class="v">core mod_so mod_watchdog http_core mod_log_config mod_logio mod_version mod_unixd mod_access_compat mod_alias mod_auth_basic mod_authn_core mod_authn_file mod_authz_core mod_authz_host mod_authz_user mod_autoindex mod_deflate mod_dir mod_env mod_filter mod_mime mod_mpm_prefork mod_negotiation mod_php7 mod_reqtimeout mod_rewrite mod_setenvif mod_status </td></tr>
</table>

<h2><a name="module_Core">Core</a></h2>
<table>
<tr><td class="e">PHP Version </td><td class="v">7.4.33 </td></tr>
<tr><td class="e">Directive </td><td class="v">Local Value </td><td class="v">Master Value </td></tr>
<tr><td class="e">allow_url_fopen </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">allow_url_include </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">arg_separator.input </td><td class="v">&amp; </td><td class="v">&amp; </td></tr>
<tr><td class="e">arg_separator.output </td><td class="v">&amp; </td><td class="v">&amp; </td></tr>
<tr><td class="e">auto_append_file </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">auto_prepend_file </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">browscap </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">default_charset </td><td class="v">UTF-8 </td><td class="v">UTF-8 </td></tr>
<tr><td class="e">default_mimetype </td><td class="v">text/html </td><td class="v">text/html </td></tr>
<tr><td class="e">disable_classes </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">disable_functions </td><td class="v">exec,passthru,shell_exec,system,proc_open,popen </td><td class="v">exec,passthru,shell_exec,system,proc_open,popen </td></tr>
<tr><td class="e">display_errors </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">display_startup_errors </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">doc_root </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">docref_ext </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">docref_root </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">enable_dl </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">enable_post_data_reading </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">error_append_string </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">error_log </td><td class="v">/var/log/apache2/error.log </td><td class="v">/var/log/apache2/error.log </td></tr>
<tr><td class="e">error_prepend_string </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">error_reporting </td><td class="v">22527 </td><td class="v">22527 </td></tr>
<tr><td class="e">expose_php </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">extension_dir </td><td class="v">/usr/lib/php/20190902 </td><td class="v">/usr/lib/php/20190902 </td></tr>
<tr><td class="e">file_uploads </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">hard_timeout </td><td class="v">2 </td><td class="v">2 </td></tr>
<tr><td class="e">highlight.comment </td><td class="v"><font style="color: #FF8000">#FF8000</font> </td><td class="v"><font style="color: #FF8000">#FF8000</font> </td></tr>
<tr><td class="e">highlight.default </td><td class="v"><font style="color: #0000BB">#0000BB</font> </td><td class="v"><font style="color: #0000BB">#0000BB</font> </td></tr>
<tr><td class="e">highlight.html </td><td class="v"><font style="color: #000000">#000000</font> </td><td class="v"><font style="color: #000000">#000000</font> </td></tr>
<tr><td class="e">highlight.keyword </td><td class="v"><font style="color: #007700">#007700</font> </td><td class="v"><font style="color: #007700">#007700</font> </td></tr>
<tr><td class="e">highlight.string </td><td class="v"><font style="color: #DD0000">#DD0000</font> </td><td class="v"><font style="color: #DD0000">#DD0000</font> </td></tr>
<tr><td class="e">html_errors </td><td class="v">Off </td><td class="v">On </td></tr>
<tr><td class="e">ignore_repeated_errors </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">ignore_repeated_source </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">ignore_user_abort </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">implicit_flush </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">include_path </td><td class="v">.:/usr/share/php </td><td class="v">.:/usr/share/php </td></tr>
<tr><td class="e">input_encoding </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">internal_encoding </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">log_errors </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">log_errors_max_len </td><td class="v">1024 </td><td class="v">1024 </td></tr>
<tr><td class="e">mail.add_x_header </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">mail.force_extra_parameters </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">mail.log </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">max_execution_time </td><td class="v">30 </td><td class="v">30 </td></tr>
<tr><td class="e">max_file_uploads </td><td class="v">20 </td><td class="v">20 </td></tr>
<tr><td class="e">max_input_nesting_level </td><td class="v">64 </td><td class="v">64 </td></tr>
<tr><td class="e">max_input_time </td><td class="v">60 </td><td class="v">60 </td></tr>
<tr><td class="e">max_input_vars </td><td class="v">1000 </td><td class="v">1000 </td></tr>
<tr><td class="e">memory_limit </td><td class="v">128M </td><td class="v">128M </td></tr>
<tr><td class="e">open_basedir </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">output_buffering </td><td class="v">4096 </td><td class="v">4096 </td></tr>
<tr><td class="e">output_encoding </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">output_handler </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">post_max_size </td><td class="v">8M </td><td class="v">8M </td></tr>
<tr><td class="e">precision </td><td class="v">14 </td><td class="v">14 </td></tr>
<tr><td class="e">realpath_cache_size </td><td class="v">4096K </td><td class="v">4096K </td></tr>
<tr><td class="e">realpath_cache_ttl </td><td class="v">120 </td><td class="v">120 </td></tr>
<tr><td class="e">register_argc_argv </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">report_memleaks </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">report_zend_debug </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">request_order </td><td class="v">GP </td><td class="v">GP </td></tr>
<tr><td class="e">sendmail_from </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">sendmail_path </td><td class="v">/usr/sbin/sendmail -t -i </td><td class="v">/usr/sbin/sendmail -t -i </td></tr>
<tr><td class="e">serialize_precision </td><td class="v">-1 </td><td class="v">-1 </td></tr>
<tr><td class="e">short_open_tag </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">SMTP </td><td class="v">localhost </td><td class="v">localhost </td></tr>
<tr><td class="e">smtp_port </td><td class="v">25 </td><td class="v">25 </td></tr>
<tr><td class="e">sys_temp_dir </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">track_errors </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">unserialize_callback_func </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">upload_max_filesize </td><td class="v">2M </td><td class="v">2M </td></tr>
<tr><td class="e">upload_tmp_dir </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">user_dir </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">user_ini.cache_ttl </td><td class="v">300 </td><td class="v">300 </td></tr>
<tr><td class="e">user_ini.filename </td><td class="v">.user.ini </td><td class="v">.user.ini </td></tr>
<tr><td class="e">variables_order </td><td class="v">GPCS </td><td class="v">GPCS </td></tr>
<tr><td class="e">xmlrpc_error_number </td><td class="v">0 </td><td class="v">0 </td></tr>
<tr><td class="e">xmlrpc_errors </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">zend.assertions </td><td class="v">-1 </td><td class="v">1 </td></tr>
<tr><td class="e">zend.detect_unicode </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">zend.enable_gc </td><td class="v">On </td><td class="v">On </td></tr>
<tr><td class="e">zend.multibyte </td><td class="v">Off </td><td class="v">Off </td></tr>
<tr><td class="e">zend.script_encoding </td><td class="v"><i>no value</i> </td><td class="v"><i>no value</i> </td></tr>
<tr><td class="e">zend.signal_check </td><td class="v">Off </td><td class="v">Off </td></tr>
</table>

</div></body></html>