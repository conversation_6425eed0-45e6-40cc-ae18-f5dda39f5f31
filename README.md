# Go 蜜罐程序

这是一个用Go语言实现的Web蜜罐程序，伪装成一个存在漏洞的Apache服务器，用于诱导和记录攻击者的行为。

## 功能特性

### 1. Debian Apache默认页面 (/)
- 完全模拟Debian系统的Apache默认欢迎页面
- 包含真实的Apache配置信息和样式

### 2. Admin目录遍历 (/admin)
- 伪装的目录遍历页面
- 显示虚假的文件列表：test.php, info.php, config.php, uploads/
- 诱导攻击者访问这些"敏感"文件

### 3. 命令执行页面 (/admin/test.php)
- 伪装存在命令执行漏洞的PHP页面
- 预设常见命令的虚假返回结果：
  - `ls` - 显示虚假的目录列表
  - `ps` - 显示虚假的进程列表
  - `whoami` - 返回 www-data
  - `id` - 显示用户ID信息
  - `pwd` - 显示当前目录
  - `uname` - 显示系统信息
  - `ifconfig` - 显示网络配置
  - `netstat` - 显示网络连接
  - `cat /etc/passwd` - 显示虚假的用户列表
  - `echo hello` - 简单的echo命令
- 未预设的命令会显示"被安全策略拦截"

### 4. PHP信息页面 (/admin/info.php)
- 完整的伪装phpinfo()页面
- 显示虚假的PHP和Apache配置信息
- 包含真实的phpinfo样式和布局

### 5. 日志记录功能
- 记录所有访问请求到控制台和文件
- 包含时间戳、IP地址、访问端点、User-Agent等信息
- 特别记录命令执行尝试和参数
- 日志文件：`honeypot.log`

## 安装和运行

### 前提条件
- Go 1.21 或更高版本
- Git (用于下载依赖)

### 安装步骤

1. 克隆或下载项目文件

2. 安装依赖：
```bash
go mod tidy
```

3. 运行蜜罐：
```bash
go run main.go
```

4. 服务器将在端口8081启动，访问：
   - http://localhost:8081 - Apache默认页面
   - http://localhost:8081/admin - 目录遍历
   - http://localhost:8081/admin/test.php - 命令执行测试
   - http://localhost:8081/admin/info.php - PHP信息

## 项目结构

```
.
├── main.go                 # 主程序文件
├── go.mod                  # Go模块文件
├── templates/              # HTML模板目录
│   ├── index.html         # Apache默认页面模板
│   ├── admin.html         # 目录遍历页面模板
│   ├── test.php.html      # 命令执行页面模板
│   └── info.php.html      # PHP信息页面模板
├── honeypot.log           # 访问日志文件（运行时生成）
└── README.md              # 说明文档
```

## 日志格式

日志记录格式示例：
```
[2023-10-15 14:32:15] IP: *************:54321 | Endpoint: TEST_PHP | User-Agent: Mozilla/5.0... | Params: cmd=ls
[2023-10-15 14:33:20] IP: *************:54322 | Endpoint: ADMIN_DIR | User-Agent: curl/7.68.0
```

## 安全注意事项

1. **仅用于研究和教育目的** - 此蜜罐仅应在受控环境中使用
2. **网络隔离** - 建议在隔离的网络环境中部署
3. **日志监控** - 定期检查日志文件，分析攻击模式
4. **资源限制** - 考虑设置适当的资源限制防止滥用

## 自定义配置

### 添加新的命令输出
在 `main.go` 中的 `commandOutputs` map 中添加新的命令和对应的输出：

```go
var commandOutputs = map[string]string{
    "your_command": "your_fake_output",
    // ...
}
```

### 修改端口
在 `main.go` 的最后一行修改端口号：

```go
log.Fatal(http.ListenAndServe(":YOUR_PORT", r))
```

### 自定义页面模板
修改 `templates/` 目录下的HTML文件来自定义页面外观和内容。

## 许可证

此项目仅供教育和研究目的使用。使用者需要遵守当地法律法规，不得用于非法用途。
