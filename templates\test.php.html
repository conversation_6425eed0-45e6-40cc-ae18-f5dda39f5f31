<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        input[type="submit"] { background-color: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        input[type="submit"]:hover { background-color: #005a87; }
        .output { background-color: #f5f5f5; border: 1px solid #ddd; padding: 15px; margin-top: 20px; border-radius: 4px; }
        .output pre { margin: 0; white-space: pre-wrap; word-wrap: break-word; }
        .error { color: #d32f2f; }
        .success { color: #388e3c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>System Command Test</h1>
        <p>This is a test page for system command execution. Enter a command below:</p>
        
        <form method="GET" action="/admin/test.php">
            <div class="form-group">
                <label for="cmd">Command:</label>
                <input type="text" id="cmd" name="cmd" placeholder="Enter command (e.g., ls, ps, whoami)" value="{{.Command}}">
            </div>
            <input type="submit" value="Execute">
        </form>
        
        {{if .Command}}
        <div class="output">
            <h3>Command: {{.Command}}</h3>
            {{if .Blocked}}
            <div class="error">
                <strong>Access Denied!</strong><br>
                Command execution blocked by security policy.<br>
                Error: Command "{{.Command}}" is not allowed.
            </div>
            {{else}}
            <div class="success">
                <strong>Output:</strong>
                <pre>{{.Output}}</pre>
            </div>
            {{end}}
        </div>
        {{end}}
        
        <div style="margin-top: 30px; font-size: 12px; color: #666;">
            <p><strong>Available commands:</strong> ls, ps, whoami, id, pwd, uname, ifconfig, netstat, cat, echo</p>
            <p><strong>Note:</strong> This is a restricted environment. Some commands may be blocked for security reasons.</p>
        </div>
    </div>
</body>
</html>
