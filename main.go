package main

import (
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gorilla/mux"
)

// CommandResult 结构体用于模板渲染
type CommandResult struct {
	Command string
	Output  string
	Blocked bool
}

// 预定义的命令输出
var commandOutputs = map[string]string{
	"ls":       "total 24\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Oct 15 14:32 admin\n-rw-r--r-- 1 <USER> <GROUP> 1234 Oct 15 14:30 index.html\n-rw-r--r-- 1 <USER> <GROUP>  856 Oct 15 14:25 config.php\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Oct 15 14:20 uploads",
	"ps":       "  PID TTY          TIME CMD\n 1234 ?        00:00:01 apache2\n 1235 ?        00:00:00 apache2\n 1236 ?        00:00:00 apache2\n 1237 ?        00:00:00 mysql\n 1238 ?        00:00:00 php-fpm",
	"whoami":   "www-data",
	"id":       "uid=33(www-data) gid=33(www-data) groups=33(www-data)",
	"pwd":      "/var/www/html",
	"uname":    "Linux webserver 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux",
	"ifconfig": "eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500\n        inet *************  netmask *************  broadcast *************\n        inet6 fe80::a00:27ff:fe4e:66a1  prefixlen 64  scopeid 0x20<link>\n        ether 08:00:27:4e:66:a1  txqueuelen 1000  (Ethernet)\n        RX packets 1234  bytes 123456 (120.5 KiB)\n        TX packets 567  bytes 56789 (55.4 KiB)",
	"netstat":  "Active Internet connections (only servers)\nProto Recv-Q Send-Q Local Address           Foreign Address         State\ntcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN\ntcp        0      0 0.0.0.0:80              0.0.0.0:*               LISTEN\ntcp        0      0 0.0.0.0:3306            0.0.0.0:*               LISTEN",
	"cat /etc/passwd": "root:x:0:0:root:/root:/bin/bash\ndaemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin\nbin:x:2:2:bin:/bin:/usr/sbin/nologin\nsys:x:3:3:sys:/dev:/usr/sbin/nologin\nwww-data:x:33:33:www-data:/var/www:/usr/sbin/nologin\nmysql:x:112:117:MySQL Server,,,:/nonexistent:/bin/false",
	"echo hello": "hello",
}

// 日志记录函数
func logAccess(r *http.Request, endpoint string, params map[string]string) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	clientIP := r.RemoteAddr
	userAgent := r.UserAgent()
	
	logEntry := fmt.Sprintf("[%s] IP: %s | Endpoint: %s | User-Agent: %s", 
		timestamp, clientIP, endpoint, userAgent)
	
	if len(params) > 0 {
		logEntry += " | Params: "
		for k, v := range params {
			logEntry += fmt.Sprintf("%s=%s ", k, v)
		}
	}
	
	log.Println(logEntry)
	
	// 写入文件日志
	file, err := os.OpenFile("honeypot.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err == nil {
		defer file.Close()
		file.WriteString(logEntry + "\n")
	}
}

// 首页处理器
func indexHandler(w http.ResponseWriter, r *http.Request) {
	logAccess(r, "INDEX", nil)
	
	tmpl, err := template.ParseFiles("templates/index.html")
	if err != nil {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "text/html")
	tmpl.Execute(w, nil)
}

// Admin目录遍历处理器
func adminHandler(w http.ResponseWriter, r *http.Request) {
	logAccess(r, "ADMIN_DIR", nil)
	
	tmpl, err := template.ParseFiles("templates/admin.html")
	if err != nil {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "text/html")
	tmpl.Execute(w, nil)
}

// test.php处理器
func testPhpHandler(w http.ResponseWriter, r *http.Request) {
	cmd := r.URL.Query().Get("cmd")
	
	params := make(map[string]string)
	if cmd != "" {
		params["cmd"] = cmd
	}
	logAccess(r, "TEST_PHP", params)
	
	result := CommandResult{
		Command: cmd,
		Output:  "",
		Blocked: false,
	}
	
	if cmd != "" {
		// 检查是否是预定义的命令
		if output, exists := commandOutputs[strings.ToLower(cmd)]; exists {
			result.Output = output
		} else {
			// 未知命令，显示被拦截
			result.Blocked = true
		}
	}
	
	tmpl, err := template.ParseFiles("templates/test.php.html")
	if err != nil {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "text/html")
	tmpl.Execute(w, result)
}

// info.php处理器
func infoPhpHandler(w http.ResponseWriter, r *http.Request) {
	logAccess(r, "INFO_PHP", nil)
	
	tmpl, err := template.ParseFiles("templates/info.php.html")
	if err != nil {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "text/html")
	tmpl.Execute(w, nil)
}

// 404处理器
func notFoundHandler(w http.ResponseWriter, r *http.Request) {
	logAccess(r, "404_NOT_FOUND", map[string]string{"path": r.URL.Path})
	
	w.WriteHeader(http.StatusNotFound)
	w.Header().Set("Content-Type", "text/html")
	fmt.Fprintf(w, `<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>404 Not Found</title>
</head><body>
<h1>Not Found</h1>
<p>The requested URL %s was not found on this server.</p>
<hr>
<address>Apache/2.4.41 (Ubuntu) Server at localhost Port 8081</address>
</body></html>`, r.URL.Path)
}

func main() {
	r := mux.NewRouter()
	
	// 路由配置
	r.HandleFunc("/", indexHandler).Methods("GET")
	r.HandleFunc("/admin", adminHandler).Methods("GET")
	r.HandleFunc("/admin/", adminHandler).Methods("GET")
	r.HandleFunc("/admin/test.php", testPhpHandler).Methods("GET")
	r.HandleFunc("/admin/info.php", infoPhpHandler).Methods("GET")
	
	// 404处理
	r.NotFoundHandler = http.HandlerFunc(notFoundHandler)
	
	// 启动服务器
	fmt.Println("蜜罐服务器启动在端口 8081...")
	fmt.Println("访问 http://localhost:8081 查看Debian Apache默认页面")
	fmt.Println("访问 http://localhost:8081/admin 查看目录遍历")
	fmt.Println("访问 http://localhost:8081/admin/test.php 进行命令测试")
	fmt.Println("访问 http://localhost:8081/admin/info.php 查看PHP信息")
	
	log.Fatal(http.ListenAndServe(":8081", r))
}
